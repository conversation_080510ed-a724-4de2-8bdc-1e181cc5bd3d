import { defineAsyncComponent } from 'vue';
import HawkLoader from '~/common/components/atoms/hawk-loader.vue';

const BiDashboard = defineAsyncComponent({
  loader: () => import('~/bi/pages/bi-dashboard.vue'),
  loadingComponent: HawkLoader,
});

const routes = [
  {
    path: '/:asset_id?/bi-dashboard',
    name: 'bi-asset-dashboard',
    component: BiDashboard,
    meta: {
      title: 'BI Dashboard',
    },
  },
];
export default routes;
