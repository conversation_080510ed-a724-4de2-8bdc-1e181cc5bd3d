<script setup>
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

defineProps({
  selectedTable: {
    type: Object,
    default: () => ({}),
  },
});

const selected_fields = ref([]);
const { getIconsForType } = useBIQueryBuilder();
const is_dropdown_open = ref(false);

function onColumnsSelected(field) {
  if (selected_fields.value.find(f => f.column.name === field.column.name && f.table.name === field.table.name && f.operator?.name === field.operator?.name))
    return;

  selected_fields.value.push(field);
}
</script>

<template>
  <div class="w-full h-screen overflow-auto">
    <div class="text-sm text-gray-700 font-semibold flex items-center gap-1 mb-4">
      <IconHawkDatabaseTwo class="size-4" /> {{ selectedTable.name }}
    </div>
    <div>
      <div v-for="({ column, operator }, index) in selected_fields" :key="column.name" class="flex items-center justify-between gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative group">
        <div class="flex items-center">
          <component :is="getIconsForType(operator ? 'formula' : column.type)" class="text-gray-600 size-4 mr-2" />
          <div v-if="operator" class="mr-1 text-gray-700">
            {{ operator.name }} {{ $t('of') }}
          </div>
          <span class="text-sm text-gray-700">{{ column.name }}</span>
        </div>
        <div class="text-gray-500 hidden group-hover:block mr-2" @click="selected_fields.splice(index, 1)">
          <IconHawkXClose class="size-4" />
        </div>
      </div>
      <div v-click-outside="() => is_dropdown_open = false">
        <bi-query-builder-columns-dropdown v-if="selected_fields.length === 0 || is_dropdown_open" :tables="[selectedTable]" @selected="onColumnsSelected" />
        <div v-else class="flex items-center justify-between">
          <Hawk-button type="link" @click="is_dropdown_open = true">
            <IconHawkPlus />   {{ $t('Add columns') }}
          </Hawk-button>
          <div class="flex items-center gap-2">
            <Hawk-button size="xxs" color="gray" type="light" icon>
              <IconHawkFilterFunnelOne class="size-3" />
            </Hawk-button>
            <Hawk-button size="xxs" color="gray" type="light" icon>
              <IconHawkSwitchVerticalOne class="size-3" />
            </Hawk-button>
            <Hawk-button size="xxs" color="gray" type="light" icon>
              <IconHawkList class="size-3" />
            </Hawk-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
