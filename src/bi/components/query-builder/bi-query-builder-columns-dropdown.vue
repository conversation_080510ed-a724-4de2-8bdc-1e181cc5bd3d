<script setup>
import { computed, ref } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

const props = defineProps({
  tables: {
    type: Array,
    default: () => ([]),
  },
});

const emit = defineEmits(['selected']);

const { getIconsForType } = useBIQueryBuilder();
const dropdown = ref(null);
const dropdown_content = ref(null);
const sub_menu = ref(null);
const search_input = ref(null);
const hovered_column = ref(null);
const hovered_table = ref(null);
const hovered_column_operators = computed(() => hovered_column.value?.operators);

// Function to detect if the dropdown is at the bottom of the page
function isDropdownAtBottom() {
  if (dropdown.value) {
    const dropdownRect = dropdown.value?.getBoundingClientRect();
    const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const dropdownHeight = dropdownContentRect.height;
    return windowHeight - dropdownRect.top < dropdownHeight;
  }
  return false;
}

// Filter the columns based on the search input and return the list of new tables that have columns that match the search input
// It should return only the columns that are matching the search input
function filterColumns(tables, search_input) {
  const new_tables = tables.map((table) => {
    const columns = table.columns.filter((column) => {
      return column.name.toLowerCase().includes(search_input.toLowerCase());
    });
    return { ...table, columns };
  });

  const filtered_tables = new_tables.filter((table) => {
    return table.columns.length > 0;
  });
  // If the search input is empty, return the original tables
  if (!search_input || filtered_tables.length === 0) {
    return tables;
  }
  return filtered_tables;
}

const filtered_tables = computed(() => {
  return filterColumns(props.tables, search_input.value || '');
});

function onColumnHovered(e, column, table) {
  hovered_column.value = column;
  hovered_table.value = table;
  // Set the sub menu position of the top based on the mouse position and the dropdown location in the screen
  const dropdownContentRect = dropdown_content.value?.getBoundingClientRect();
  const rect = e.srcElement.getBoundingClientRect();
  // If the position is at the bottom of the screen, set the top position to the bottom of the dropdown
  sub_menu.value.style.left = `${rect.left + rect.width}px`;
  if ((e.clientY + dropdownContentRect.height) > window.innerHeight) {
    sub_menu.value.style.bottom = `${window.innerHeight - rect.bottom + 5}px`;
    sub_menu.value.style.top = 'auto';
  }
  // If the position is at the top of the screen, set the top position to the top of the dropdown
  else {
    sub_menu.value.style.top = `${rect.top - 10}px`;
    sub_menu.value.style.bottom = 'auto';
  }
}

function onOperatorClicked(operator) {
  emit('selected', { column: hovered_column.value, operator, table: hovered_table.value });
}

function onColumnClicked(column, table) {
  emit('selected', { column, table });
}
</script>

<template>
  <div ref="dropdown" class="bi-columns-dropdown relative h-10" @mouseleave="hovered_column = null">
    <div ref="dropdown_content" class="shadow-lg border border-gray-200 rounded-lg absolute w-full flex flex-col" :class="{ 'bottom-0': isDropdownAtBottom(), 'top-0': !isDropdownAtBottom() }">
      <div class="relative" :class="{ 'order-2': isDropdownAtBottom() }">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <IconHawkSearch class="text-gray-400 size-4" />
        </div>
        <input
          v-model="search_input"
          name="input"
          :placeholder="$t('Search for a field')"
          class="block w-full py-1.5 text-gray-900 pl-10 placeholder:text-gray-500 focus:!ring-0 sm:text-sm sm:leading-6 rounded-lg"
        >
      </div>
      <div class="bi-columns-dropdown-content min-h-[300px] max-h-80 overflow-auto scrollbar" :class="{ 'border-t': !isDropdownAtBottom(), 'border-b': isDropdownAtBottom() }">
        <div v-for="table in filtered_tables" :key="table.name">
          <div class="text-sm text-gray-700 font-semibold flex items-center gap-1 px-3 py-2">
            <IconHawkDatabaseTwo class="size-4" /> {{ table.name }}
          </div>
          <div class="pb-2">
            <div v-for="column in table.columns" :key="column.name" class="flex items-center justify-between gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative" @mouseenter="e => onColumnHovered(e, column, table)" @click="onColumnClicked(column, table)">
              <div class="flex items-center">
                <component :is="getIconsForType(column.type)" class="text-gray-600 size-4 mr-2" />
                <span class="text-sm text-gray-700">{{ column.name }}</span>
              </div>
              <div v-if="column.operators" class="text-gray-500 size-4 mr-2">
                <IconHawkChevronRight />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="hovered_column_operators" ref="sub_menu" class="dropdown-sub-menu shadow-lg border border-gray-200 rounded-lg fixed w-[300px] h-[300px] left-[330px] bg-white">
      <div v-for="operator in hovered_column_operators" :key="operator.name" class="flex items-center gap-2 pl-6 py-1 cursor-pointer hover:bg-gray-50 rounded-lg relative" @click="onOperatorClicked(operator)">
        <span class="text-sm text-gray-700">{{ operator.name }}</span>
      </div>
    </div>
  </div>
</template>
