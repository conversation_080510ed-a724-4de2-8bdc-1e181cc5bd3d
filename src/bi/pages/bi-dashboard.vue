<script setup>
import { useModal } from 'vue-final-modal';
import BiCreateWidget from '~/bi/components/bi-create-widget.vue';

const create_widget = useModal({
  component: BiCreateWidget,
  attrs: {
    onClose() {
      create_widget.close();
    },
  },
});

function createWidget() {
  create_widget.open();
}
</script>

<template>
  <div class="flex h-full">
    <div class="w-1/5 p-4">
      BI Dashboard (Details and Metadata)
    </div>
    <!-- Vertical line -->
    <div class="w-full h-[calc(100vh-65px)] border-l p-4">
      <div class="flex items-center gap-3">
        BI Dashboard
        <HawkPageHeaderTabs :tabs="[{ uid: '1', label: 'Tab 1' }, { uid: '2', label: 'Tab 2' }]" active_item="1" />
        <HawkButton
          icon
          type="light"
          color="gray"
        >
          <IconHawkPlus />
        </HawkButton>
      </div>
      <hr class="my-4">
      <div class="flex justify-between items-center">
        <div>
          Dashboard name
        </div>
        <div class="flex items-center gap-3">
          <HawkButton type="text" @click="createWidget">
            <IconHawkPlus class="text-primary-700" />
            <span class="whitespace-nowrap text-primary-700">
              {{ $t('Add Widget') }}
            </span>
          </HawkButton>
          <HawkButton
            color="error"
          >
            {{ $t('Delete') }}
          </HawkButton>
          <HawkButton>
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </div>
      <hr class="my-4">
      <div>
        Draggable Grid
      </div>
    </div>
  </div>
</template>
