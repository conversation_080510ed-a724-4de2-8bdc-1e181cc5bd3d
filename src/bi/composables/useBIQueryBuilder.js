import calendar from '~icons/hawk/calendar';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkTypeOne from '~icons/hawk/type-one';

function getIconsForType(type) {
  const icons_type_map = {
    string: IconHawkTypeOne,
    number: IconHawkHashTwo,
    date: calendar,
    function: IconHawkFormula,
    formula: IconHawkFormula,
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

export function useBIQueryBuilder() {
  return {
    getIconsForType,
  };
}
